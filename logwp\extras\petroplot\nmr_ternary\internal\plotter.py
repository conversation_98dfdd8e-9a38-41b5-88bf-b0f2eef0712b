"""logwp.extras.petroplot.nmr_ternary.internal.plotter - NMR三元图内部绘图引擎

本模块包含生成NMR三元图的核心绘图逻辑。它被设计为纯粹的“绘图引擎”，
其行为完全由外部传入的配置对象驱动，不包含任何业务逻辑或数据解析。

职责划分:
- 本函数负责根据 `config` 对象的配置，决定 **画什么** (what to plot)。
- `selectors` 对象负责从 `data` DataFrame 中 **选择哪些列**。
- `plot_profile` 对象负责决定 **怎么画** (how to plot, e.g., fonts, sizes, line styles)。

Architecture
------------
层次/依赖: petroplot/nmr_ternary内部实现层，被facade层调用
设计原则: 关注点分离、配置驱动、无副作用
"""

from typing import Optional, Dict, Any
from typing import List, Tuple
import pandas as pd
import plotly.graph_objects as go

from logwp.extras.petroplot.common import LayoutManager, LegendManager
from logwp.extras.petroplot.common.config import ContinuousColorConfig, CategoricalColorConfig
from logwp.extras.petroplot.common.plotter_abc import TernaryPlotter as AbstractTernaryPlotter
from logwp.infra.color_utils import hex_to_rgba
from ..config import NmrTernaryPlotConfig, NmrTernaryColumnSelectors
from logwp.extras.plotting import PlotProfile


class NmrTernaryPlotter(AbstractTernaryPlotter):
    """
    一个专门用于绘制NMR三元图的绘图器。

    它继承自通用的 TernaryPlotter 抽象基类，并实现了
    绘制三元图背景、坐标轴和数据点所需的特定逻辑。
    """

    def __init__(
        self,
        config: NmrTernaryPlotConfig,
        selectors: NmrTernaryColumnSelectors,
        plot_profile: PlotProfile,
        data: pd.DataFrame,
        subtitle: Optional[str] = None,
    ):
        # 注意：三元图目前只处理单个DataFrame，所以data_dict只有一个元素
        super().__init__(config, selectors, plot_profile, {"main": data}, subtitle)

    def _setup_figure(self) -> None:
        self.fig = go.Figure()

    def _draw_main_plot(self) -> None:
        """绘制三元图的背景分区。"""
        bg_config = self.config.background_regions
        if not bg_config.enable or not bg_config.regions or not bg_config.region_colors:
            return

        region_style = self.plot_profile.artist_props.get("background_region", {})
        line_style = region_style.get("line", {"width": 0})

        for name, coords in bg_config.regions.items():
            if name not in bg_config.region_colors:
                continue

            closed_coords = coords + [coords[0]]
            a_coords = [p[0] for p in closed_coords]
            b_coords = [p[1] for p in closed_coords]
            c_coords = [p[2] for p in closed_coords]

            fill_color = hex_to_rgba(bg_config.region_colors[name], bg_config.opacity)

            self.fig.add_trace(go.Scatterternary({
                'mode': 'lines',
                'a': a_coords, 'b': b_coords, 'c': c_coords,
                'fill': 'toself',
                'fillcolor': fill_color,
                'line': line_style,
                'name': name,
                'showlegend': True,
                'hoverinfo': 'name',
                'legendgroup': 'regions'
            }))

    def _add_data_traces(self) -> None:
        """根据配置添加数据点轨迹。"""
        df = self.data_dict["main"]
        data_with_color = df
        data_without_color = pd.DataFrame()

        if self.selectors.color_col and self.config.distinguish_null_color:
            notna_mask = df[self.selectors.color_col].notna()
            data_with_color = df[notna_mask]
            data_without_color = df[~notna_mask]

        marker_style = self.plot_profile.artist_props.get("data_marker", {})
        TraceType = self._get_plot_trace_type()

        # 1. 绘制空值点
        if not data_without_color.empty:
            null_style = self.config.null_marker_style
            self.fig.add_trace(TraceType({
                'mode': 'markers',
                **self._get_scatter_coords(data_without_color),
                'marker': {
                    'color': null_style.color,
                    'size': marker_style.get("size", 8) * null_style.size_factor,
                    'symbol': null_style.symbol
                },
                'name': null_style.label,
                'legendgroup': 'data_points',
                'hoverinfo': 'skip'
            }))

        # 2. 绘制有值点
        if not data_with_color.empty:
            marker_props = {
                'size': marker_style.get("size", 8),
                'line': marker_style.get("line", {"width": 0.5, "color": "white"}),
            }

            # --- Color Mapping ---
            if self.selectors.color_col and self.config.color_mapping:
                color_map_cfg = self.config.color_mapping
                if isinstance(color_map_cfg, ContinuousColorConfig):
                    # 最终解决方案：直接在Trace级别构建和布局颜色轴，以规避全局布局冲突
                    colorbar_title = color_map_cfg.colorbar_title or self.selectors.color_col
                    colorbar_style = self.plot_profile.artist_props.get("colorbar", {})

                    marker_props.update({
                        'color': data_with_color[self.selectors.color_col],
                        'colorscale': color_map_cfg.colorscale,
                        'reversescale': color_map_cfg.reverse_scale,
                        'cmin': color_map_cfg.cmin,
                        'cmax': color_map_cfg.cmax,
                        'showscale': True,
                        'colorbar': {
                            'x': 0.92,
                            'xanchor': 'left',
                            'y': 1,
                            'yanchor': 'top',
                            'len': colorbar_style.get("len", 0.55),
                            'thickness': colorbar_style.get("thickness", 10),
                            'title': {
                                'text': colorbar_title,
                                'side': colorbar_style.get("title_side", "right"),
                                'font': colorbar_style.get("titlefont", {"size": 16})},
                            'tickfont': colorbar_style.get("tickfont", {"size": 14})}
                    })

            # --- Symbol Mapping ---
            if self.selectors.symbol_col and self.config.symbol_mapping and self.config.symbol_mapping.symbol_map:
                marker_props['symbol'] = data_with_color[self.selectors.symbol_col].map(self.config.symbol_mapping.symbol_map)

            self.fig.add_trace(TraceType({
                'mode': 'markers',
                **self._get_scatter_coords(data_with_color),
                'marker': marker_props,
                'name': self.config.data_marker_style.label,
                'legendgroup': 'data_points',
                'customdata': self._build_custom_data(data_with_color),
                'hovertemplate': self._build_hover_template()
            }))

    def _get_scatter_coords(self, df: pd.DataFrame) -> Dict[str, Any]:
        """为三元图返回 a, b, c 坐标。"""
        return {
            'a': df[self.selectors.macro_col],
            'b': df[self.selectors.micro_col],
            'c': df[self.selectors.meso_col]
        }

    def _get_plot_trace_type(self) -> Any:
        """返回三元散点图的轨迹类型。"""
        return go.Scatterternary

    def _setup_ternary_axes(self) -> Dict[str, Any]:
        """构建三元图的坐标轴配置。"""
        axis_style = self.plot_profile.artist_props.get("ternary_axis", {})
        grid_style = self.plot_profile.artist_props.get("ternary_grid", {})

        def get_axis_config(axis_cfg):
            return {
                'title': {'text': f'<b>{axis_cfg.label}</b>' if axis_cfg.bold_title else axis_cfg.label, 'font': {'color': axis_cfg.color}},
                'min': 0,
                'linewidth': axis_style.get("linewidth", 2),
                'linecolor': axis_cfg.color,
                'ticks': axis_style.get("ticks", "outside"),
                'tickfont': {'color': axis_cfg.color},
                'gridcolor': axis_cfg.color,
                'gridwidth': grid_style.get("width", 0.5),
                'griddash': grid_style.get("dash", "dot"),
                'dtick': axis_cfg.dtick,
            }

        return {
            'ternary': {
                'sum': self.config.normalization_total,
                'aaxis': get_axis_config(self.config.macro_axis),
                'baxis': get_axis_config(self.config.micro_axis),
                'caxis': get_axis_config(self.config.meso_axis),
                'bgcolor': self.config.plot_bgcolor,
            }
        }

    def _apply_layout(self) -> None:
        """应用整体布局和样式，使用新的组合式辅助工具。"""
        axes_config = self._setup_ternary_axes()
        has_colorbar, _ = self._find_coloraxis_info()

        # 新策略：如果存在颜色轴，则手动收缩主绘图区域，为颜色轴腾出空间。
        if has_colorbar:
            axes_config['ternary']['domain'] = {'x': [0, 0.9]}

        # 1. 获取包含固定尺寸和边距的基础布局
        layout_mgr = LayoutManager(self.config, self.plot_profile, self.subtitle)
        layout_updates = layout_mgr.build_base_layout()

        # 绘图器作为“总指挥”，明确设定画布尺寸，因为后续的布局策略（如domain收缩）
        # 依赖于一个固定的、已知的画布。
        layout_updates.update({
            'width': self.plot_profile.figure_props.get("width", 800),
            'height': self.plot_profile.figure_props.get("height", 700),
        })

        # 2. 使用 LegendManager 获取图例专属布局，并向其明确告知所有信息
        legend_mgr = LegendManager(self.config, self.plot_profile)
        legend_updates = legend_mgr.build_legend_layout(
            has_standard_legend=self._has_standard_legend_items()
        )

        # 3. 简单合并所有布局更新
        layout_updates.update(axes_config)
        layout_updates.update(legend_updates)

        # 4. 一次性应用所有更新
        self.fig.update_layout(**layout_updates)

    def _find_coloraxis_info(self) -> Tuple[bool, str]:
        """确定图中是否存在颜色轴，并返回其名称。"""
        has_colorbar = (
            isinstance(self.config.color_mapping, ContinuousColorConfig) and
            self.selectors.color_col is not None
        )
        # 对于nmr_ternary，我们不使用多系列，因此颜色轴总是默认的'coloraxis'
        return has_colorbar, "coloraxis"

    def _has_standard_legend_items(self) -> bool:
        """检查是否存在需要显示在标准图例中的项。"""
        has_bg_regions = self.config.background_regions.enable
        # 仅当同时指定了颜色列和有效的颜色映射时，才认为需要分类颜色图例
        has_categorical_color = (
            isinstance(self.config.color_mapping, CategoricalColorConfig) and
            self.selectors.color_col is not None and
            bool(self.config.color_mapping.color_map)
        )
        # 仅当同时指定了符号列和有效的符号映射时，才认为需要分类符号图例
        has_categorical_symbol = (
            self.selectors.symbol_col is not None and
            self.config.symbol_mapping is not None and
            bool(self.config.symbol_mapping.symbol_map)
        )
        return has_bg_regions or has_categorical_color or has_categorical_symbol

    def _build_hover_template(self) -> str:
        """为三元图构建悬停提示框模板。"""
        template = (
            f'<b>{self.config.macro_axis.label}</b>: %{{a:.1f}}%<br>'
            f'<b>{self.config.micro_axis.label}</b>: %{{b:.1f}}%<br>'
            f'<b>{self.config.meso_axis.label}</b>: %{{c:.1f}}%<br>'
        )
        if self.selectors.color_col:
            color_map_cfg = self.config.color_mapping
            format_code = ":.2f" if isinstance(color_map_cfg, ContinuousColorConfig) else ""
            template += f'<b>{self.selectors.color_col}</b>: %{{marker.color{format_code}}}<br>'

        # customdata的构建由基类处理，这里只需构建模板字符串
        custom_data_cols = self._build_custom_data_columns(self.data_dict["main"])
        for i, col in enumerate(custom_data_cols):
            is_numeric = pd.api.types.is_numeric_dtype(self.data_dict["main"][col])
            format_code = ":.2f" if is_numeric else ""
            template += f'<b>{col}</b>: %{{customdata[{i}]{format_code}}}<br>'

        template += '<extra></extra>'
        return template

    def _build_custom_data_columns(self, df: pd.DataFrame) -> List[str]:
        """【辅助方法】获取将包含在 customdata 中的列名列表。"""
        custom_data_cols = []
        if self.selectors.symbol_col and self.selectors.symbol_col in df.columns:
            custom_data_cols.append(self.selectors.symbol_col)
        if self.selectors.hover_extra_cols:
            custom_data_cols.extend([c for c in self.selectors.hover_extra_cols if c in df.columns])
        return custom_data_cols

    def _build_custom_data(self, df: pd.DataFrame) -> Optional[Any]:
        """【辅助方法】构建用于悬停提示的 customdata。"""
        custom_data_cols = self._build_custom_data_columns(df)
        if not custom_data_cols:
            return None
        return df[custom_data_cols].values


def draw_nmr_ternary_plot(
    data: pd.DataFrame,
    config: NmrTernaryPlotConfig,
    selectors: NmrTernaryColumnSelectors,
    plot_profile: PlotProfile,
    subtitle: Optional[str] = None
) -> go.Figure:
    """
    【内部绘图逻辑】纯粹的绘图函数，将配置和样式应用于数据以生成Plotly图表。
    """
    plotter = NmrTernaryPlotter(
        config=config,
        selectors=selectors,
        plot_profile=plot_profile,
        data=data,
        subtitle=subtitle
    )
    return plotter.plot()
