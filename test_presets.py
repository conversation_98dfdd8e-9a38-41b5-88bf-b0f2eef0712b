import sys
sys.path.insert(0, '.')

try:
    # 执行presets.py文件内容
    exec(open('logwp/extras/petroplot/nmr_ternary/presets.py').read(), globals())
    print('函数存在:', 'create_publication_ready_perm_config' in globals())
    print('所有定义的名称:', [name for name in globals() if not name.startswith('_') and not hasattr(__builtins__, name)])
except Exception as e:
    print('执行错误:', e)
    import traceback
    traceback.print_exc()
