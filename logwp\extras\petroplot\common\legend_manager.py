"""logwp.extras.petroplot.common.legend_manager - 智能图例管理器

本模块定义了 `LegendManager`，一个纯粹的布局计算器，负责处理复杂的、
协同的图例布局问题。

职责:
- 根据调用者明确告知的信息（如图中是否有标准图例和颜色轴），计算出最佳布局。
- 权威地管理 `legend` 和 `coloraxis` 布局对象。
- 智能地调整边距，为外部图例提供空间。

设计原则:
- 组合优于继承：被具体的绘图器实例化并调用。
- 单一职责原则：只处理图例布局，与通用布局分离。
- 显式告知：不动态扫描Figure对象，其行为由输入参数决定。
"""

from typing import Dict, Any, Optional
from logwp.extras.plotting import PlotProfile


class LegendManager:
    """
    一个配置化的辅助类，用于构建智能的、协同的Plotly图例布局。
    """

    def __init__(self, config: Any, plot_profile: PlotProfile):
        """
        初始化 LegendManager。

        Args:
            config: 绘图器特定的配置对象 (e.g., NmrTernaryPlotConfig)。
            plot_profile: 绘图样式配置文件。
        """
        self.config = config
        self.plot_profile = plot_profile

    def build_legend_layout(
        self,
        has_standard_legend: bool
    ) -> Dict[str, Any]:
        """
        构建图例相关的布局更新字典。

        根据新架构，本方法只负责标准图例 (`legend` 对象) 的生成。
        颜色轴 (Colorbar) 的布局已完全由具体的绘图器在Trace级别处理。

        Args:
            has_standard_legend: 一个布尔值，明确指示图中是否包含标准图例项。

        Returns:
            一个包含 `legend` 和 `showlegend` 键的布局更新字典。
        """
        layout_updates: Dict[str, Any] = {}

        # 仅当配置和实际情况都要求时，才构建并显示标准图例
        show_legend_flag = self.config.legend.show and has_standard_legend
        layout_updates['showlegend'] = show_legend_flag
        if show_legend_flag:
            layout_updates['legend'] = self._get_base_legend_properties()

        return layout_updates

    def _get_base_legend_properties(self) -> Dict[str, Any]:
        """构建标准图例的基础属性字典。"""
        return {
            'title_text': self.config.legend.title,
            'x': self.config.legend.x, 'y': self.config.legend.y,
            'xanchor': self.config.legend.xanchor, 'yanchor': self.config.legend.yanchor,
            'tracegroupgap': 20,
            'font': {'size': self.plot_profile.rc_params.get("legend.fontsize", 11)}
        }
