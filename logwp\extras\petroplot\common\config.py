"""logwp.extras.petroplot.common.config - 通用绘图配置模型

本模块定义了所有 petroplot 组件可复用的、与具体图表类型无关的Pydantic配置模型。
这些模型主要负责定义“视觉映射”逻辑，如颜色、符号、图例等。
"""

from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field

class ColorBarConfig(BaseModel):
    """定义颜色条（Color Bar）的布局和表现层配置。"""
    show: bool = Field(True, description="是否显示颜色条。")
    title: Optional[str] = Field(None, description="颜色条标题。如果为None，将根据颜色映射列自动生成。")
    orientation: Literal['v', 'h'] = Field('v', description="颜色条的方向。")
    x: float = Field(1.02, description="颜色条在x轴上的位置。")
    y: float = Field(0.5, description="颜色条在y轴上的位置。")
    xanchor: Literal["auto", "left", "center", "right"] = Field("left", description="颜色条在x轴上的锚点。")
    yanchor: Literal["auto", "top", "middle", "bottom"] = Field("middle", description="颜色条在y轴上的锚点。")
    len: float = Field(0.7, description="颜色条相对于绘图区高度/宽度的长度。", ge=0, le=1)
    thickness: int = Field(15, description="颜色条的厚度（像素）。", gt=0)

class ContinuousColorConfig(BaseModel):
    """定义连续数值如何映射到颜色。只关心数据映射逻辑，不关心布局。"""
    mapping_type: Literal["continuous"] = "continuous"
    colorscale: str = Field("Viridis", description="Plotly颜色映射方案。")
    reverse_scale: bool = Field(False, description="是否反转颜色映射的顺序。")
    cmin: Optional[float] = Field(None, description="颜色条范围的最小值。")
    cmax: Optional[float] = Field(None, description="颜色条范围的最大值。")
    log_transform: bool = Field(False, description="是否对颜色映射列取对数(log10)。")
    log_clip_lower_bound: float = Field(1e-6, description="当log_transform为True时，用于替换非正数的下限值。", gt=0)


class CategoricalColorConfig(BaseModel):
    """用于分类/名义值的颜色映射配置。"""
    mapping_type: Literal["categorical"] = "categorical"
    color_map: Optional[Dict[str, str]] = Field(None, description="（推荐）一个将类别值精确映射到颜色的字典，如 {'Sandstone': 'yellow', 'Shale': 'gray'}。")
    color_sequence: Optional[List[str]] = Field(None, description="（备选）一个颜色列表。如果未提供color_map，则按顺序为唯一类别分配颜色。")
    category_order: Optional[List[str]] = Field(None, description="（可选）一个列表，用于指定图例中类别的显示顺序。")


class SymbolConfig(BaseModel):
    """用于分类/名义值的标记符号映射配置。"""
    symbol_map: Optional[Dict[str, str]] = Field(None, description="（推荐）一个将类别值精确映射到Plotly标记符号的字典，如 {'Sandstone': 'circle', 'Shale': 'square'}。")
    symbol_sequence: Optional[List[str]] = Field(None, description="（备选）一个标记符号列表。如果未提供symbol_map，则按顺序为唯一类别分配符号。")
    category_order: Optional[List[str]] = Field(None, description="（可选）一个列表，用于指定图例中类别的显示顺序。")


class LegendConfig(BaseModel):
    """图例的配置。"""
    show: bool = Field(True, description="是否显示图例。")
    title: str = Field("图例", description="图例的标题。")
    x: float = Field(1.02, description="图例在x轴上的位置 (0-1代表绘图区内，>1代表绘图区右侧)。")
    y: float = Field(1.0, description="图例在y轴上的位置 (0-1代表绘图区内)。")
    xanchor: Literal["auto", "left", "center", "right"] = Field("left", description="图例在x轴上的锚点。")
    yanchor: Literal["auto", "top", "middle", "bottom"] = Field("top", description="图例在y轴上的锚点。")


class DataMarkerStyle(BaseModel):
    """定义主数据点在图例中的显示配置。"""
    label: str = Field("有数据", description="图例中有颜色映射的数据点的标签。")


class NullMarkerStyle(BaseModel):
    """当颜色列存在空值并需要区分显示时，用于定义空值点样式的配置。"""
    symbol: str = Field("x", description="空值点的标记符号 (e.g., 'x', 'circle-open')")
    size_factor: float = Field(0.8, description="空值点相对于正常点的大小比例因子 (e.g., 0.8 表示80%)", gt=0)
    color: str = Field("lightgray", description="空值点的颜色")
    label: str = Field("无数据", description="图例中空值点的标签")


class BaseLine(BaseModel):
    """所有线条配置的基类，定义了共享的样式属性。"""
    color: Optional[str] = Field(None, description="线条的颜色。")
    width: Optional[float] = Field(None, description="线条的宽度（像素）。")
    dash: Optional[str] = Field(None, description="线条的样式 (e.g., 'solid', 'dash', 'dot')。")

class LineStyle(BaseLine):
    """定义数据艺术家（如系列线、对角线）的线条样式。具有具体的默认值。"""
    width: Optional[float] = Field(2.0, description="线条的宽度。")
    dash: Optional[str] = Field("solid", description="线条的样式 (e.g., 'solid', 'dash', 'dot', 'dashdot')。")

class DiagonalLineConfig(BaseModel):
    """定义参考对角线的配置。"""
    show: bool = Field(False, description="是否显示对角线。")
    slope: float = Field(1.0, description="对角线的斜率 (y = slope * x + intercept)。")
    intercept: float = Field(0.0, description="对角线的截距。")
    line_style: LineStyle = Field(default_factory=LineStyle, description="对角线的线条样式。如果未指定，将从PlotProfile获取默认样式。")

class LineConfig(BaseLine):
    """定义布局元素（如坐标轴线、网格线）的线条配置。所有值默认为None，以继承自PlotProfile。"""
    show: Optional[bool] = Field(None, description="是否显示此线条。")

class TickConfig(BaseModel):
    """定义一个刻度线集合（主要或次要）的完整配置。"""
    show: Optional[bool] = Field(None, description="是否显示此级别的刻度线。")
    dtick: Optional[Any] = Field(None, description="刻度的间隔。对于对数轴，可以是'L<f>'或'D1'/'D2'。")
    nticks: Optional[int] = Field(None, description="在两个更高一级刻度之间显示的刻度数量（主要用于次要刻度）。")
    ticklen: Optional[float] = Field(None, description="刻度线的长度（像素）。")
    tickwidth: Optional[float] = Field(None, description="刻度线的宽度（像素）。")
    tickcolor: Optional[str] = Field(None, description="刻度线的颜色。")
    showlabels: Optional[bool] = Field(None, description="是否显示刻度标签（通常仅对主要刻度有效）。")
    grid_line: Optional[LineConfig] = Field(None, description="与此级别刻度线关联的网格线配置。")


class AxisConfig(BaseModel):
    """定义笛卡尔坐标系单个坐标轴的逻辑和内容。"""
    label: Optional[str] = Field(None, description="坐标轴的显示标签。如果为None，将自动使用曲线名。")
    min: Optional[float] = Field(None, description="坐标轴的最小值。如果为None，将自动确定。")
    max: Optional[float] = Field(None, description="坐标轴的最大值。如果为None，将自动确定。")
    scale: Literal["linear", "log"] = Field("linear", description="坐标轴的尺度类型。")
    log_clip_lower_bound: float = Field(1e-6, description="当scale为'log'时，用于替换非正数的下限值。", gt=0)
    axis_line: Optional[LineConfig] = Field(None, description="（可选）配置坐标轴本身的线条样式。")
    major_ticks: Optional[TickConfig] = Field(None, description="（可选）配置主要刻度线及其关联的主网格线。")
    minor_ticks: Optional[TickConfig] = Field(None, description="（可选）配置次要刻度线及其关联的次要网格线。")

class MarginalPlotConfig(BaseModel):
    """定义边缘分布图的配置。"""
    show: bool = Field(False, description="是否显示此轴的边缘分布图。")
    plot_type: Literal["histogram", "rug", "box"] = Field("histogram", description="边缘图的类型。")
    size: float = Field(0.2, description="边缘图相对于主图所占的空间比例。", gt=0, le=0.5)
    style_props: Dict[str, Any] = Field(default_factory=dict, description="传递给边缘图绘图函数的额外样式参数，如 {'marker_color': 'grey'}。")

class CartesianPlotConfig(BaseModel):
    """所有基于笛卡尔坐标系的图表的通用配置基类。"""
    title: str = Field("Plot", description="图表主标题。")
    x_axis: AxisConfig = Field(default_factory=AxisConfig, description="共享的X轴配置。")
    y_axis: AxisConfig = Field(default_factory=AxisConfig, description="共享的Y轴配置。")
    plot_bgcolor: Optional[str] = Field(None, description="绘图区域的背景颜色。如果为None，将使用PlotProfile中的默认值。")
    diagonal_line: Optional[DiagonalLineConfig] = Field(None, description="（可选）在图表上添加一条参考对角线。")
    marginal_x: Optional[MarginalPlotConfig] = Field(None, description="（可选）为X轴添加边缘分布图。")
    marginal_y: Optional[MarginalPlotConfig] = Field(None, description="（可选）为Y轴添加边缘分布图。")
    color_bar: Optional[ColorBarConfig] = Field(None, description="（可选）配置全局共享的颜色条布局。")
