"""logwp.extras.petroplot.nmr_ternary.facade - NMR三元图门面函数

本模块实现了NMR三元图组件的公共API，为不同使用场景提供入口。

Architecture
------------
层次/依赖: petroplot/nmr_ternary门面层，调用内部绘图引擎
设计原则: 清晰接口、关注点分离、可复用
"""

from typing import Optional, Dict, Any, Tuple, List
import pandas as pd
import numpy as np
from pathlib import Path
from logwp.infra import get_logger
import json
from logwp.extras.plotting import PlotProfile, registry, SaveConfig
from logwp.extras.plotting import PlotProfile, registry
from logwp.extras.tracking import RunContext
from logwp.models.constants import WpDepthRole
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.models.curve import CurveExpansionMode

from logwp.extras.petroplot.common import save_and_register_plots
from .config import NmrTernaryPlotConfig, NmrTernaryDataSelectors, NmrTernaryColumnSelectors, ContinuousColorConfig, ColorBarConfig
from .constants import NmrTernaryPlotArtifacts, NmrTernaryPlotProfiles
from .internal import draw_nmr_ternary_plot

logger = get_logger(__name__)


def generate_nmr_ternary_plot(
    config: NmrTernaryPlotConfig,
    selectors: NmrTernaryColumnSelectors,
    data: pd.DataFrame,
    plot_profile: PlotProfile,
) -> Tuple[Dict[str, Any], pd.DataFrame]:
    """
    【普通门面】从DataFrame生成三元图。

    根据配置对数据进行验证、归一化、转换和拆分，然后调用内部绘图引擎。

    Args:
        config: 绘图的逻辑与表现层配置。
        selectors: 包含物理列名的选择器。
        data: 输入的DataFrame。
        plot_profile: 绘图的美学样式配置。

    Returns:
        一个元组，包含:
        - 一个图表字典，键为拆分值（或'main'），值为Plotly Figure对象。
        - 一个处理后的DataFrame，可作为数据快照保存。
    """
    # 1. 数据验证
    required_cols = [selectors.macro_col, selectors.micro_col, selectors.meso_col]
    if selectors.color_col: required_cols.append(selectors.color_col)
    if selectors.symbol_col: required_cols.append(selectors.symbol_col)
    if selectors.split_col: required_cols.append(selectors.split_col)

    required_cols = [col for col in required_cols if col is not None]
    missing_cols = [col for col in required_cols if col not in data.columns]
    if missing_cols:
        raise ValueError(f"数据中缺少必需的列: {missing_cols}")

    # 2. 数据准备 (创建副本以避免修改原始对象)
    plot_data = data.copy()
    plot_selectors = selectors.copy(deep=True)
    plot_config = config.copy(deep=True)

    # 2a. 数据归一化
    if plot_config.normalize_data:
        component_cols = [plot_selectors.macro_col, plot_selectors.micro_col, plot_selectors.meso_col]
        row_sums = plot_data[component_cols].sum(axis=1)
        row_sums[row_sums == 0] = 1.0  # 避免除以零
        plot_data[component_cols] = plot_data[component_cols].div(row_sums, axis=0) * plot_config.normalization_total

    # 2b. 颜色轴对数转换
    if plot_selectors.color_col and isinstance(plot_config.color_mapping, ContinuousColorConfig) and plot_config.color_mapping.log_transform:
        color_map_cfg = plot_config.color_mapping
        # 创建一个新的列用于绘图，以避免修改原始数据列
        original_color_col = selectors.color_col
        log_color_col_name = f"{original_color_col}_log10"

        # 检查并警告非正数值
        non_positive_mask = plot_data[original_color_col] <= 0
        num_non_positive = non_positive_mask.sum()
        if num_non_positive > 0:
            logger.warning(
                f"在对列 '{original_color_col}' 进行对数转换时，发现 {num_non_positive} 个非正数值。"
                f"这些值将被替换为下限值 {color_map_cfg.log_clip_lower_bound} 以进行计算。"
            )

        # 使用 .clip() 方法将所有非正数值替换为配置中指定的下限，以确保对数转换的数学有效性。
        clipped_series = plot_data[original_color_col].clip(lower=color_map_cfg.log_clip_lower_bound)
        plot_data[log_color_col_name] = np.log10(clipped_series)

        # 更新选择器和配置以使用新的对数转换列
        plot_selectors.color_col = log_color_col_name
        # 【重构】更新 color_bar 的标题，而不是旧的 color_mapping
        if plot_config.color_bar is None:
            plot_config.color_bar = ColorBarConfig()
        if plot_config.color_bar.title is None:
             plot_config.color_bar.title = f"log10({selectors.color_col})"

    # 3. 检查是否需要按列拆分绘图
    if plot_selectors.split_col:
        unique_splits = plot_data[plot_selectors.split_col].dropna().unique()
        if len(unique_splits) > plot_config.max_split_plots:
            raise ValueError(f"拆分后的子图数量 ({len(unique_splits)}) 超过了设定的最大值 ({plot_config.max_split_plots})。")

        figs_dict = {}
        for split_value in unique_splits:
            subset_df = plot_data[plot_data[plot_selectors.split_col] == split_value]
            if subset_df.empty:
                continue

            subtitle = plot_config.split_subtitle_template.format(
                split_col=plot_selectors.split_col,
                value=split_value
            )
            fig = draw_nmr_ternary_plot(
                data=subset_df,
                config=plot_config,
                selectors=plot_selectors,
                plot_profile=plot_profile,
                subtitle=subtitle
            )
            figs_dict[split_value] = fig

        return figs_dict, plot_data
    else:
        # 不拆分，只生成一个图
        fig = draw_nmr_ternary_plot(
            data=plot_data,
            config=plot_config,
            selectors=plot_selectors,
            plot_profile=plot_profile
        )
        return {"main": fig}, plot_data


def replot_nmr_ternary_from_snapshot(
    snapshot_path: Path,
    logic_config_path: Path,
    plot_profile: PlotProfile,
    output_path: Path,
) -> None:
    """
    【重绘函数】从数据和逻辑快照复现图表。

    此函数严格遵循可复现性原则，直接使用快照数据进行绘图，
    不执行任何额外的数据处理（如归一化或对数转换）。

    Note:
        如果原始图表生成时被拆分 (split_by_curve)，此函数将忽略拆分，
        并基于完整的数据快照绘制一个聚合的图表。

    Args:
        snapshot_path: 数据快照的路径 (.csv)。
        logic_config_path: 包含绘图配置和列选择器的逻辑快照路径 (.json)。
        plot_profile: 绘图的美学样式配置。
        output_path: 输出图表的完整路径 (e.g., 'path/to/figure.png')。
    """
    # 1. 加载数据快照和配置
    snapshot_df = pd.read_csv(snapshot_path)

    logic_snapshot = json.loads(logic_config_path.read_text())
    config = NmrTernaryPlotConfig(**logic_snapshot['plot_config'])
    selectors = NmrTernaryColumnSelectors(**logic_snapshot['column_selectors'])

    # 2. 调用内部绘图引擎
    # 注意：这里直接使用快照数据，不进行任何预处理，以保证复现的精确性。
    # 同时，忽略拆分逻辑，始终生成一个聚合图。
    fig = draw_nmr_ternary_plot(
        data=snapshot_df,
        config=config,
        selectors=selectors,
        plot_profile=plot_profile
    )

    # 3. 保存图表，根据输出路径的后缀名决定格式
    output_suffix = output_path.suffix.lower()
    if output_suffix == ".html":
        fig.write_html(output_path)
    else:
        # 支持 .png, .svg, .pdf, .jpeg 等
        fig.write_image(output_path)


def _resolve_selectors(
    bundle: WpDataFrameBundle,
    selectors: NmrTernaryDataSelectors
) -> NmrTernaryColumnSelectors:
    """【私有辅助函数】将逻辑曲线名解析为物理列名。"""
    def expand(curves: Optional[List[str]]) -> Optional[List[str]]:
        if not curves:
            return None
        return bundle.curve_metadata.expand_curve_names(curves, mode=CurveExpansionMode.DATAFRAME)

    def expand_single(curve: Optional[str]) -> Optional[str]:
        if not curve:
            return None
        expanded = expand([curve])
        # 确保展开后只返回一个列名
        if expanded and len(expanded) == 1:
            return expanded[0]
        elif expanded:
            raise ValueError(f"曲线 '{curve}' 预期为一维曲线，但展开为多个列: {expanded}")
        return None

    well_curves = bundle.curve_metadata.get_well_identifier_curves()
    if not well_curves:
        raise ValueError("无法在数据Bundle的元数据中找到井名标识曲线。")

    depth_curves = bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
    if not depth_curves:
        raise ValueError("无法在数据Bundle的元数据中找到单深度参考曲线。")

    return NmrTernaryColumnSelectors(
        macro_col=expand_single(selectors.macro_curve),
        micro_col=expand_single(selectors.micro_curve),
        meso_col=expand_single(selectors.meso_curve),
        color_col=expand_single(selectors.color_curve),
        symbol_col=expand_single(selectors.symbol_curve),
        split_col=expand_single(selectors.split_by_curve),
        hover_extra_cols=expand(selectors.hover_extra_curves),
        well_col=well_curves[0],
        depth_col=depth_curves[0],
    )


def run_nmr_ternary_plot_step(
    config: NmrTernaryPlotConfig,
    selectors: NmrTernaryDataSelectors,
    ctx: RunContext,
    bundle: WpDataFrameBundle,
    *,
    prefix: str,
    register_artifacts: bool = True,
    plot_profile: Optional[PlotProfile] = None,
) -> Dict[str, Any]:
    """
    【步骤门面】执行可追踪的三元图绘制步骤。
    """
    # 1. 获取并创建步骤目录
    step_dir = ctx.get_step_dir(f"{prefix}_nmr_ternary")

    # 2. 解析曲线名
    column_selectors = _resolve_selectors(bundle, selectors)

    # 3. 获取绘图样式配置
    profile = plot_profile or registry.get(NmrTernaryPlotProfiles.DEFAULT.value)

    # 4. 调用普通门面执行核心逻辑
    figs_dict, snapshot_df = generate_nmr_ternary_plot(
        config=config,
        selectors=column_selectors,
        data=bundle.data,
        plot_profile=profile
    )

    # 5. 保存和注册产物
    if register_artifacts:
        # 5a. 保存完整的逻辑配置快照，确保可复现性
        logic_snapshot = {
            "plot_config": config.model_dump(mode="json"),
            "column_selectors": column_selectors.model_dump(mode="json")
        }
        logic_config_path = step_dir / "logic_config.json"
        logic_config_path.write_text(json.dumps(logic_snapshot, indent=2))
        ctx.register_artifact(
            logic_config_path.relative_to(ctx.run_dir),
            f"{prefix}.{NmrTernaryPlotArtifacts.LOGIC_CONFIG.value}"
        )

        # 5b. 保存数据快照 (所有子图共享一个快照)
        snapshot_path = step_dir / "plot_snapshot.csv"
        snapshot_df.to_csv(snapshot_path, index=False)
        ctx.register_artifact(
            snapshot_path.relative_to(ctx.run_dir),
            f"{prefix}.{NmrTernaryPlotArtifacts.DATA_SNAPSHOT.value}"
        )

        # 5c. 使用通用辅助函数保存和注册所有图表
        save_and_register_plots(
            ctx=ctx,
            figs_dict=figs_dict,
            profile=profile,
            step_dir=step_dir,
            prefix=prefix,
            plot_base_name_template="ternary_plot_{split_value}",
            artifact_plot_prefix=NmrTernaryPlotArtifacts.PLOT_PREFIX.value,
            output_formats=config.output_formats,
        )

    return {"status": "completed", "figures": figs_dict, "snapshot_df": snapshot_df}
